---
description: Repository Information Overview
alwaysApply: true
---

# Yemen GPS Project Information

## Summary
This project is a GPS mapping application for Yemen with both web and mobile components. It uses Node.js for the backend server and has an Android mobile application. The project integrates with Google Maps API and uses PostgreSQL for database storage. The application appears to focus on providing mapping and navigation services specific to Yemen.

## Structure
- **android/**: Contains the Android mobile application code
- **backend/**: Backend server code and API implementation
- **config/**: Configuration files for the application
- **data/**: Data files for the application, likely including map data
- **database/**: Database-related files and possibly migrations
- **docs/**: Documentation for the project
- **logs/**: Application logs with error tracking
- **public/**: Web frontend assets (CSS, JavaScript, images)
- **scripts/**: Utility scripts for maintenance and deployment
- **server/**: Server-side code with API endpoints and routes
- **utils/**: Utility functions and helpers

## Language & Runtime
**Language**: JavaScript (Node.js)
**Version**: Not explicitly specified
**Database**: PostgreSQL
**Mobile**: Android (Java/Kotlin)
**Frontend**: HTML, CSS, JavaScript

## Configuration
**Environment Variables** (.env):
```
DB_TYPE=postgres
DB_USER=yemen
DB_PASSWORD=admin
DB_HOST=localhost
DB_PORT=5432
DB_NAME=yemen_gps
NODE_ENV=development
PORT=3000
JWT_SECRET=yemen_gps_secret_key_for_authentication_and_security
CORS_ORIGIN=*
LOG_LEVEL=info
```

## Main Components

### Web Application
**Frontend**: 
- Located in `/public` directory
- Uses JavaScript for client-side functionality
- Contains CSS styling and various assets
- Includes admin user management functionality
- Uses FontAwesome for icons
- Contains routing functionality in `/public/js/routing`

### Server API
**Structure**:
- API endpoints in `/server/api`
- Route definitions in `/server/routes`
- Uses JWT for authentication
- RESTful API design pattern

### Android Application
**Structure**:
- Standard Android app architecture
- Java-based implementation
- Contains layouts, drawables, and resources
- Organized in standard Android project structure with res/ and java/ directories

## Database
**Type**: PostgreSQL
**Configuration**:
- User: yemen
- Host: localhost
- Port: 5432
- Database: yemen_gps
- Connection timeout: 10 seconds
- SSL Mode: prefer

## File Storage
**Map Files**: Stored in ./maps directory
**Uploads**: Stored in ./uploads directory
**Logs**: Stored in ./logs directory

## Integration
**External Services**:
- Google Maps API integration (API key configured in environment)

## Environment Configuration
**Development Settings**:
- Development environment configured
- CORS enabled for all origins
- Logging level set to info
- JWT authentication implemented for security